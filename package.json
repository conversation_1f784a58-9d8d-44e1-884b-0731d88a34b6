{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxtjs/tailwindcss": "^6.14.0", "@vite-pwa/nuxt": "^1.0.4", "@vueuse/nuxt": "^13.4.0", "idb-keyval": "^6.2.2", "jsqr": "^1.4.0", "nuxt": "^3.17.5", "quagga": "^0.12.1", "sharp": "^0.34.2", "tailwindcss": "^3.4.17", "vue": "^3.5.17", "vue-router": "^4.5.1"}}
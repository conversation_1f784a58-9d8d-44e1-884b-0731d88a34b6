@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for mobile-first design */
@layer base {
  html {
    font-family: system-ui, sans-serif;
    -webkit-text-size-adjust: 100%;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-center block w-full;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 font-medium py-3 px-6 rounded-lg transition-colors duration-200 text-center block w-full;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900/20 p-4 border border-gray-200 dark:border-gray-700;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400;
  }

  .store-card {
    @apply card hover:shadow-lg dark:hover:shadow-gray-900/30 transition-shadow duration-200 cursor-pointer;
  }

  .theme-toggle {
    @apply p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200;
  }
}

/* Touch-friendly styles for mobile */
@media (max-width: 768px) {
  .btn-primary,
  .btn-secondary {
    @apply py-4 text-lg;
  }
  
  .store-card {
    @apply p-6;
  }
  
  .input-field {
    @apply py-4 text-lg;
  }
}
